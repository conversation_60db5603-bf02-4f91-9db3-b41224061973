import {
  createAndMint,
  mplTokenMetadata,
  TokenStandard
} from '@metaplex-foundation/mpl-token-metadata'
import {
  generateSigner,
  percentAmount,
  keypairIdentity,
  publicKey
} from '@metaplex-foundation/umi'
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults'
import { irysUploader } from '@metaplex-foundation/umi-uploader-irys'
import { base58 } from '@metaplex-foundation/umi/serializers'
import bs58 from 'bs58'
import { 
  TOKEN_PROGRAM_ID, 
  AuthorityType, 
  createSetAuthorityInstruction 
} from '@solana/spl-token'
import {
  Connection,
  Keypair,
  PublicKey,
  Transaction,
  sendAndConfirmTransaction
} from '@solana/web3.js'

// Get RPC endpoint from environment
const getRpcEndpoint = () => {
  const network = process.env.VITE_SOLANA_NETWORK || 'devnet'
  const customRpc = process.env.VITE_SOLANA_RPC_URL
  
  if (customRpc) {
    return customRpc
  }
  
  switch (network) {
    case 'mainnet-beta':
      return 'https://api.mainnet-beta.solana.com'
    case 'testnet':
      return 'https://api.testnet.solana.com'
    case 'devnet':
    default:
      return 'https://api.devnet.solana.com'
  }
}

// New function to prepare transaction for frontend signing
export const prepareTokenTransaction = async ({
  name,
  symbol,
  uri,
  decimals = 9,
  totalSupply,
  walletAddress
}) => {
  try {
    console.log('🔧 Preparing token creation transaction...')

    const rpcEndpoint = getRpcEndpoint()
    console.log('🌐 Using RPC endpoint:', rpcEndpoint)

    // Initialize UMI with the user's wallet as identity
    const umi = createUmi(rpcEndpoint)
      .use(mplTokenMetadata())
      .use(irysUploader())

    // Create a temporary keypair for transaction building (won't be used for signing)
    console.log('🔗 Using connected wallet as transaction payer:', walletAddress)
    const tempKeypair = Keypair.generate()
    const umiKeypair = umi.eddsa.createKeypairFromSecretKey(tempKeypair.secretKey)
    umi.use(keypairIdentity(umiKeypair))

    console.log('🪙 Generating mint signer...')
    const mintSigner = generateSigner(umi)

    // Calculate total supply with decimals
    const totalSupplyWithDecimals = BigInt(totalSupply) * BigInt(10 ** decimals)

    console.log('📝 Token parameters:', {
      name,
      symbol,
      uri,
      decimals,
      totalSupply: totalSupplyWithDecimals.toString(),
      mintAddress: mintSigner.publicKey
    })

    // Create the transaction instruction
    console.log('📦 Building transaction...')
    const mintAndCreateIx = createAndMint(umi, {
      mint: mintSigner,
      name,
      symbol,
      uri,
      sellerFeeBasisPoints: percentAmount(0), // No royalties
      decimals,
      amount: totalSupplyWithDecimals,
      tokenOwner: publicKey(walletAddress), // Send tokens to user's wallet
      tokenStandard: TokenStandard.Fungible,
    })

    // Build the transaction but don't send it
    const transactionBuilder = await mintAndCreateIx.setLatestBlockhash(umi)
    const transaction = await transactionBuilder.build(umi)

    console.log('✅ Transaction prepared successfully!')
    console.log('🪙 Token address:', mintSigner.publicKey)

    return {
      transaction: bs58.encode(transaction.serializedMessage),
      mintAddress: mintSigner.publicKey,
      mintSignerSecretKey: bs58.encode(mintSigner.secretKey),
      success: true
    }

  } catch (error) {
    console.error('❌ Transaction preparation failed:', error)
    throw new Error(`Transaction preparation failed: ${error.message}`)
  }
}

export const createToken = async ({
  name,
  symbol,
  uri,
  decimals = 9,
  totalSupply,
  revokeAuthorities = true,
  walletAddress
}) => {
  try {
    console.log('🔧 Initializing Solana connection...')

    const rpcEndpoint = getRpcEndpoint()
    console.log('🌐 Using RPC endpoint:', rpcEndpoint)

    // Initialize UMI with the user's wallet as identity
    const umi = createUmi(rpcEndpoint)
      .use(mplTokenMetadata())
      .use(irysUploader())

    // Create a temporary keypair for transaction building (won't be used for signing)
    console.log('🔗 Using connected wallet as transaction payer:', walletAddress)
    const tempKeypair = Keypair.generate()
    const umiKeypair = umi.eddsa.createKeypairFromSecretKey(tempKeypair.secretKey)
    umi.use(keypairIdentity(umiKeypair))

    // Create web3.js connection for authority operations
    const connection = new Connection(rpcEndpoint, 'confirmed')

    console.log('🪙 Generating mint signer...')
    const mintSigner = generateSigner(umi)

    // Calculate total supply with decimals
    const totalSupplyWithDecimals = BigInt(totalSupply) * BigInt(10 ** decimals)

    console.log('📝 Token parameters:', {
      name,
      symbol,
      uri,
      decimals,
      totalSupply: totalSupplyWithDecimals.toString(),
      mintAddress: mintSigner.publicKey
    })

    // Create and mint token
    console.log('🚀 Creating and minting token...')
    const mintAndCreateIx = createAndMint(umi, {
      mint: mintSigner,
      name,
      symbol,
      uri,
      sellerFeeBasisPoints: percentAmount(0), // No royalties
      decimals,
      amount: totalSupplyWithDecimals,
      tokenOwner: publicKey(walletAddress), // Send tokens to user's wallet
      tokenStandard: TokenStandard.Fungible,
    })

    const tx = await mintAndCreateIx.sendAndConfirm(umi)
    const signature = base58.deserialize(tx.signature)[0]

    console.log('✅ Token created successfully!')
    console.log('📊 Transaction signature:', signature)
    console.log('🪙 Token address:', mintSigner.publicKey)

    let authorityRevocationSignature = null

    // Revoke authorities if requested
    if (revokeAuthorities) {
      console.log('🔒 Revoking mint and freeze authorities...')
      
      try {
        const mintPublicKey = new PublicKey(mintSigner.publicKey)
        const transaction = new Transaction()

        // Add instruction to revoke mint authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            tempKeypair.publicKey,
            AuthorityType.MintTokens,
            null
          )
        )

        // Add instruction to revoke freeze authority
        transaction.add(
          createSetAuthorityInstruction(
            mintPublicKey,
            tempKeypair.publicKey,
            AuthorityType.FreezeAccount,
            null
          )
        )

        // Send and confirm the authority revocation transaction
        authorityRevocationSignature = await sendAndConfirmTransaction(
          connection,
          transaction,
          [tempKeypair]
        )

        console.log('✅ Authorities revoked successfully!')
        console.log('📊 Authority revocation signature:', authorityRevocationSignature)
      } catch (authorityError) {
        console.warn('⚠️ Failed to revoke authorities:', authorityError.message)
        // Don't fail the entire operation if authority revocation fails
      }
    }

    return {
      signature,
      tokenAddress: mintSigner.publicKey,
      authorityRevocationSignature,
      success: true
    }

  } catch (error) {
    console.error('❌ Token creation failed:', error)
    throw new Error(`Token creation failed: ${error.message}`)
  }
}

// Function to execute token creation from prepared transaction data
export const executeTokenFromPreparedData = async ({
  transactionData,
  mintSignerSecretKey,
  mintAddress,
  metadataUri,
  walletAddress
}) => {
  try {
    console.log('🔧 Executing token creation from prepared data...')

    const rpcEndpoint = getRpcEndpoint()
    console.log('🌐 Using RPC endpoint:', rpcEndpoint)

    // Initialize UMI
    const umi = createUmi(rpcEndpoint)
      .use(mplTokenMetadata())
      .use(irysUploader())

    // Create the mint signer from the secret key
    const mintSignerSecretKeyBuffer = bs58.decode(mintSignerSecretKey)
    const mintSigner = umi.eddsa.createKeypairFromSecretKey(mintSignerSecretKeyBuffer)

    // Use the mint signer as identity for this transaction
    umi.use(keypairIdentity(mintSigner))

    // Deserialize and send the transaction
    const transactionBuffer = bs58.decode(transactionData)
    const transaction = {
      serializedMessage: transactionBuffer,
      signatures: []
    }

    console.log('🚀 Sending transaction to Solana...')
    const result = await umi.rpc.sendTransaction(transaction)
    const signature = base58.deserialize(result.signature)[0]

    console.log('✅ Token created successfully!')
    console.log('📊 Transaction signature:', signature)
    console.log('🪙 Token address:', mintAddress)

    return {
      signature,
      tokenAddress: mintAddress,
      success: true
    }

  } catch (error) {
    console.error('❌ Token execution failed:', error)
    throw new Error(`Token execution failed: ${error.message}`)
  }
}
