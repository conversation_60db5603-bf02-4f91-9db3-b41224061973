import { createToken, prepareTokenTransaction, executeTokenFromPreparedData } from '../services/tokenService.js'
import { uploadToIPFS, uploadMetadataToIPFS } from '../services/ipfsService.js'
import { processImage } from '../services/imageService.js'

export const createTokenHandler = async (req, res) => {
  try {
    console.log('🚀 Token creation request received')

    // Check if this is an execution request (from prepared transaction)
    if (req.body.transactionData) {
      return await executeTokenCreation(req, res)
    }

    // Extract form data for traditional creation
    const {
      name,
      symbol,
      description,
      totalSupply,
      decimals,
      revokeAuthorities,
      walletAddress
    } = req.body

    // Validate required fields
    if (!name || !symbol || !description || !totalSupply || !walletAddress) {
      return res.status(400).json({
        error: 'Missing required fields: name, symbol, description, totalSupply, walletAddress'
      })
    }

    console.log('📝 Token details:', {
      name,
      symbol,
      description: description.substring(0, 50) + '...',
      totalSupply,
      decimals,
      revokeAuthorities,
      walletAddress: walletAddress.substring(0, 8) + '...',
      hasImage: !!req.file
    })

    let imageUri = null

    // Process and upload image if provided
    if (req.file) {
      console.log('🖼️ Processing image...')
      
      try {
        // Process image (resize, optimize)
        const processedImage = await processImage(req.file.buffer, {
          width: 512,
          height: 512,
          quality: 90
        })

        // Upload image to IPFS
        console.log('📤 Uploading image to IPFS...')
        imageUri = await uploadToIPFS(processedImage, `${symbol.toLowerCase()}-logo.png`)
        console.log('✅ Image uploaded:', imageUri)
      } catch (imageError) {
        console.error('❌ Image processing/upload failed:', imageError)
        return res.status(400).json({
          error: 'Failed to process or upload image: ' + imageError.message
        })
      }
    }

    // Create metadata object
    const metadata = {
      name,
      symbol,
      description,
      image: imageUri || '',
      attributes: [],
      properties: {
        files: imageUri ? [{
          uri: imageUri,
          type: 'image/png'
        }] : [],
        category: 'image'
      },
      creators: [{
        address: walletAddress,
        verified: false,
        share: 100
      }]
    }

    // Upload metadata to IPFS
    console.log('📤 Uploading metadata to IPFS...')
    const metadataUri = await uploadMetadataToIPFS(metadata, `${symbol.toLowerCase()}-metadata.json`)
    console.log('✅ Metadata uploaded:', metadataUri)

    // Create token on Solana
    console.log('🪙 Creating token on Solana...')
    const tokenResult = await createToken({
      name,
      symbol,
      uri: metadataUri,
      decimals: parseInt(decimals) || 9,
      totalSupply: totalSupply,
      revokeAuthorities: revokeAuthorities === 'true',
      walletAddress
    })

    console.log('✅ Token created successfully!')
    console.log('📊 Result:', {
      signature: tokenResult.signature,
      tokenAddress: tokenResult.tokenAddress,
      metadataUri
    })

    // Return success response
    res.json({
      success: true,
      signature: tokenResult.signature,
      tokenAddress: tokenResult.tokenAddress,
      metadataUri,
      imageUri,
      message: 'Token created successfully!'
    })

  } catch (error) {
    console.error('❌ Token creation failed:', error)

    // Determine error type and status code
    let statusCode = 500
    let errorMessage = error.message || 'Token creation failed'

    if (error.message?.includes('insufficient funds')) {
      statusCode = 400
      errorMessage = 'Insufficient SOL balance for token creation'
    } else if (error.message?.includes('wallet')) {
      statusCode = 400
      errorMessage = 'Invalid wallet address or wallet connection issue'
    } else if (error.message?.includes('network')) {
      statusCode = 503
      errorMessage = 'Solana network connection issue. Please try again.'
    }

    res.status(statusCode).json({
      error: errorMessage,
      ...(process.env.NODE_ENV === 'development' && {
        details: error.stack,
        originalError: error.message
      })
    })
  }
}

export const prepareTokenHandler = async (req, res) => {
  try {
    console.log('🚀 Token preparation request received')

    // Extract form data
    const {
      name,
      symbol,
      description,
      totalSupply,
      decimals,
      walletAddress
    } = req.body

    // Validate required fields
    if (!name || !symbol || !description || !totalSupply || !walletAddress) {
      return res.status(400).json({
        error: 'Missing required fields: name, symbol, description, totalSupply, walletAddress'
      })
    }

    console.log('📝 Token details:', {
      name,
      symbol,
      description: description.substring(0, 50) + '...',
      totalSupply,
      decimals,
      walletAddress: walletAddress.substring(0, 8) + '...',
      hasImage: !!req.file
    })

    // Process image if provided
    let imageUri = ''
    if (req.file) {
      console.log('🖼️ Processing image...')
      const processedImage = await processImage(req.file.buffer)
      console.log('📤 Uploading image to IPFS...')
      imageUri = await uploadToIPFS(processedImage, `${symbol.toLowerCase()}-logo.png`)
      console.log('✅ Image uploaded:', imageUri)
    }

    // Create metadata object
    const metadata = {
      name,
      symbol,
      description,
      image: imageUri || '',
      attributes: [],
      properties: {
        files: imageUri ? [{
          uri: imageUri,
          type: 'image/png'
        }] : [],
        category: 'image'
      },
      creators: [{
        address: walletAddress,
        verified: false,
        share: 100
      }]
    }

    // Upload metadata to IPFS
    console.log('📤 Uploading metadata to IPFS...')
    const metadataUri = await uploadMetadataToIPFS(metadata, `${symbol.toLowerCase()}-metadata.json`)
    console.log('✅ Metadata uploaded:', metadataUri)

    // Prepare transaction for frontend signing
    console.log('📦 Preparing transaction for signing...')
    const transactionData = await prepareTokenTransaction({
      name,
      symbol,
      uri: metadataUri,
      decimals: parseInt(decimals) || 9,
      totalSupply: totalSupply,
      walletAddress
    })

    console.log('✅ Transaction prepared successfully!')

    res.json({
      success: true,
      transaction: transactionData.transaction,
      mintAddress: transactionData.mintAddress,
      mintSignerSecretKey: transactionData.mintSignerSecretKey,
      metadataUri
    })

  } catch (error) {
    console.error('❌ Transaction preparation failed:', error)

    // Determine error type and status code
    let statusCode = 500
    let errorMessage = error.message || 'Transaction preparation failed'

    if (error.message?.includes('wallet')) {
      statusCode = 400
      errorMessage = 'Invalid wallet address'
    } else if (error.message?.includes('network')) {
      statusCode = 503
      errorMessage = 'Solana network connection issue. Please try again.'
    }

    res.status(statusCode).json({
      error: errorMessage,
      ...(process.env.NODE_ENV === 'development' && {
        details: error.stack,
        originalError: error.message
      })
    })
  }
}

// Function to execute token creation from prepared transaction data
const executeTokenCreation = async (req, res) => {
  try {
    console.log('🚀 Executing token creation from prepared data')

    const {
      transactionData,
      mintSignerSecretKey,
      mintAddress,
      metadataUri,
      walletAddress
    } = req.body

    console.log('📝 Execution details:', {
      mintAddress,
      metadataUri,
      walletAddress: walletAddress.substring(0, 8) + '...'
    })

    // Execute the token creation using the prepared data
    const result = await executeTokenFromPreparedData({
      transactionData,
      mintSignerSecretKey,
      mintAddress,
      metadataUri,
      walletAddress
    })

    console.log('✅ Token creation executed successfully!')

    res.json({
      success: true,
      signature: result.signature,
      tokenAddress: result.tokenAddress,
      metadataUri
    })

  } catch (error) {
    console.error('❌ Token execution failed:', error)

    res.status(500).json({
      error: error.message || 'Token execution failed',
      ...(process.env.NODE_ENV === 'development' && {
        details: error.stack,
        originalError: error.message
      })
    })
  }
}

// Simple token creation using server wallet (for demo purposes)
export const createTokenWithWalletHandler = async (req, res) => {
  try {
    console.log('🚀 Creating token with server wallet funding')

    const {
      name,
      symbol,
      description,
      totalSupply,
      decimals,
      revokeAuthorities,
      walletAddress,
      metadataUri
    } = req.body

    console.log('📝 Token creation details:', {
      name,
      symbol,
      totalSupply,
      decimals,
      walletAddress: walletAddress.substring(0, 8) + '...',
      metadataUri
    })

    // Create token using the original method but with proper wallet
    const result = await createToken({
      name,
      symbol,
      uri: metadataUri,
      decimals: parseInt(decimals) || 9,
      totalSupply: totalSupply,
      revokeAuthorities: revokeAuthorities === true,
      walletAddress
    })

    console.log('✅ Token created successfully!')

    res.json({
      success: true,
      signature: result.signature,
      tokenAddress: result.tokenAddress,
      metadataUri
    })

  } catch (error) {
    console.error('❌ Token creation failed:', error)

    res.status(500).json({
      error: error.message || 'Token creation failed',
      ...(process.env.NODE_ENV === 'development' && {
        details: error.stack,
        originalError: error.message
      })
    })
  }
}
