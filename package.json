{"name": "solana-token-creator", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["solana", "spl-token", "blockchain", "crypto"], "author": "", "license": "ISC", "description": "Comprehensive Solana SPL Token Creation Platform", "dependencies": {"@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/mpl-toolbox": "^0.10.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@metaplex-foundation/umi-signer-wallet-adapters": "^1.2.0", "@metaplex-foundation/umi-uploader-irys": "^1.2.0", "@metaplex-foundation/umi-web3js-adapters": "^1.2.0", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.24", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.98.2", "axios": "^1.6.0", "bn.js": "^5.2.2", "bs58": "^6.0.0", "cors": "^2.8.5", "decimal.js": "^10.5.0", "express": "^4.18.2", "lucide-react": "^0.400.0", "multer": "^1.4.5-lts.1", "pinata-web3": "^0.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "sharp": "^0.33.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.0.0", "vite": "^5.0.0"}}